# Device Isolation Fix for Multi-Session Setup

## Problem Description

The multi-session setup was experiencing device isolation issues where both app sessions would connect to the same device instead of maintaining separate device connections. This was causing:

- Both browser tabs showing the same device screen
- Device connections being overwritten when a new session connected
- Inability to test multiple devices simultaneously

## Root Cause Analysis

The issue was caused by **global variable sharing** in the Flask application:

1. **Global `current_device_id`**: All Flask app instances shared the same global `current_device_id` variable
2. **Session State Confusion**: When one session connected to a device, it would overwrite the global state for all other sessions
3. **Screenshot URL Issues**: Screenshot endpoints were using the global device ID instead of session-specific device IDs

### Code Location of the Problem

In `app/app.py` lines 812-819:
```python
# Set as current device
current_device_id = device_id  # ❌ This was global across all sessions
current_device = device_id  # For backward compatibility

# Set global device_controller for backward compatibility with health check
global device_controller, player, action_factory
device_controller = controller  # ❌ Also global
```

## Solution Implemented

### 1. Session-Based Device Tracking

Replaced global `current_device_id` with Flask session-based storage:

```python
# Session-based device tracking - each session maintains its own current device
def get_session_device_id():
    """Get the current device ID for this session"""
    return session.get('current_device_id')

def set_session_device_id(device_id):
    """Set the current device ID for this session"""
    session['current_device_id'] = device_id

def clear_session_device_id():
    """Clear the current device ID for this session"""
    session.pop('current_device_id', None)
```

### 2. Updated API Endpoints

Modified all device-related API endpoints to use session-specific device tracking:

- `/api/device/connect` - Now sets session-specific device ID
- `/api/device/disconnect` - Uses session-specific device ID
- `/api/action/text` - Uses session device controller
- `/api/execute_test_case` - Uses session device controller
- `/screenshot` - Uses session device ID when no deviceId parameter provided

### 3. Frontend Session Isolation

Updated JavaScript modules to include session information:

- **deviceConnector.js**: Includes sessionId in screenshot URLs
- **main.js**: Sends session_id in connection requests and screenshot URLs
- Session ID stored in localStorage for persistence across page reloads

### 4. Device Controller Management

The device controllers are still stored in a global dictionary (`device_controllers`) but are now accessed using session-specific device IDs, ensuring proper isolation.

## Files Modified

1. **`app/app.py`**:
   - Added session-based device tracking functions
   - Updated device connection/disconnection endpoints
   - Modified all API endpoints to use session-specific device controllers

2. **`app/static/js/main.js`**:
   - Updated `connectToDevice()` to send session_id
   - Modified screenshot URLs to include sessionId parameter
   - Enhanced fallback screenshot URLs with session information

3. **`app/static/js/modules/deviceConnector.js`**:
   - Updated automatic screenshot refresh to include sessionId
   - Enhanced device pre-selection with session awareness

## Testing the Fix

### Manual Testing Steps

1. **Start two app instances**:
   ```bash
   # Terminal 1
   python run.py
   
   # Terminal 2  
   python run.py --port 8081 --appium-port 4724
   ```

2. **Open both in separate browser tabs**:
   - Session 1: http://localhost:8080
   - Session 2: http://localhost:8081

3. **Connect different devices** (or same device to test isolation):
   - In Session 1: Select Device A and click Connect
   - In Session 2: Select Device B and click Connect

4. **Verify isolation**:
   - Each session should show its own device screen
   - Screenshots should be device-specific
   - Device connections should remain independent

### Automated Testing

Run the provided test script:
```bash
python test_session_isolation.py
```

This script will:
- Test device list retrieval for both sessions
- Connect to devices in each session
- Verify screenshot isolation
- Check session state isolation
- Clean up connections

## Expected Behavior After Fix

✅ **Each browser tab maintains its own device connection**
✅ **Screenshots are session and device-specific**  
✅ **Multiple devices can be tested simultaneously**
✅ **Device connections don't interfere with each other**
✅ **Session state is properly isolated**

## Backward Compatibility

The fix maintains backward compatibility by:
- Keeping global variables for legacy code that might depend on them
- Preserving existing API endpoint signatures
- Maintaining the same frontend interface

## Future Improvements

1. **Complete Global Variable Removal**: Eventually remove all global device variables once all code is verified to use session-based tracking
2. **Session Management UI**: Add UI indicators showing which session is connected to which device
3. **Session Cleanup**: Implement automatic session cleanup when browser tabs are closed
4. **Enhanced Logging**: Add session ID to all log messages for better debugging

## Troubleshooting

If device isolation issues persist:

1. **Clear browser storage**: Clear localStorage and sessionStorage
2. **Restart app instances**: Stop and restart both Flask instances
3. **Check logs**: Look for session ID in connection logs
4. **Verify ports**: Ensure each instance is using different ports
5. **Test with different browsers**: Use different browsers for each session to ensure complete isolation
