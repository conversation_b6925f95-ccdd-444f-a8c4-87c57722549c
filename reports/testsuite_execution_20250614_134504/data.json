{"name": "UI Execution 14/06/2025, 13:49:53", "testCases": [{"name": "health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1175ms", "action_id": "UppP3ZuqY6", "screenshot_filename": "UppP3ZuqY6.png", "report_screenshot": "UppP3ZuqY6.png", "resolved_screenshot": "screenshots/UppP3ZuqY6.png", "clean_action_id": "UppP3ZuqY6", "prefixed_action_id": "al_UppP3ZuqY6", "action_id_screenshot": "screenshots/UppP3ZuqY6.png"}, {"name": "Add Log: Launched App Successfully (with screenshot)", "status": "passed", "duration": "615ms", "action_id": "Successful", "screenshot_filename": "Successful.png", "report_screenshot": "Successful.png", "resolved_screenshot": "screenshots/Successful.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1308ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Edit link is clicked (with screenshot)", "status": "passed", "duration": "450ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1656ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Done link is clicked (with screenshot)", "status": "passed", "duration": "595ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}, {"name": "Wait for 1 ms", "status": "passed", "duration": "1001ms", "action_id": "vjBGuN5y9x", "screenshot_filename": "vjBGuN5y9x.png", "report_screenshot": "vjBGuN5y9x.png", "resolved_screenshot": "screenshots/vjBGuN5y9x.png", "clean_action_id": "vjBGuN5y9x", "prefixed_action_id": "al_vjBGuN5y9x", "action_id_screenshot": "screenshots/vjBGuN5y9x.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1057ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "clean_action_id": "4kBvNvFi5i", "prefixed_action_id": "al_4kBvNvFi5i", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Add Log: App is closed (with screenshot)", "status": "passed", "duration": "1228ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}]}, {"name": "apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1159ms", "action_id": "UppP3ZuqY6", "screenshot_filename": "UppP3ZuqY6.png", "report_screenshot": "UppP3ZuqY6.png", "resolved_screenshot": "screenshots/UppP3ZuqY6.png", "clean_action_id": "UppP3ZuqY6", "prefixed_action_id": "al_UppP3ZuqY6", "action_id_screenshot": "screenshots/UppP3ZuqY6.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "932ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1562ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1032ms", "action_id": "vjBGuN5y9x", "screenshot_filename": "vjBGuN5y9x.png", "report_screenshot": "vjBGuN5y9x.png", "resolved_screenshot": "screenshots/vjBGuN5y9x.png", "clean_action_id": "vjBGuN5y9x", "prefixed_action_id": "al_vjBGuN5y9x", "action_id_screenshot": "screenshots/vjBGuN5y9x.png"}, {"name": "Add Log: Closed App Successfully (with screenshot)", "status": "passed", "duration": "1132ms", "action_id": "Successful", "screenshot_filename": "Successful.png", "report_screenshot": "Successful.png", "resolved_screenshot": "screenshots/Successful.png"}, {"name": "Execute Test Case: health2 (9 steps)", "status": "passed", "duration": "0ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "clean_action_id": "4kBvNvFi5i", "prefixed_action_id": "al_4kBvNvFi5i", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"] (Recovery)", "status": "passed", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}]}, {"name": "apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1159ms", "action_id": "UppP3ZuqY6", "screenshot_filename": "UppP3ZuqY6.png", "report_screenshot": "UppP3ZuqY6.png", "resolved_screenshot": "screenshots/UppP3ZuqY6.png", "clean_action_id": "UppP3ZuqY6", "prefixed_action_id": "al_UppP3ZuqY6", "action_id_screenshot": "screenshots/UppP3ZuqY6.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1255ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1545ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1259ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Clicked on Edit link successfully (with screenshot)", "status": "passed", "duration": "433ms", "action_id": "successful", "screenshot_filename": "successful.png", "report_screenshot": "successful.png", "resolved_screenshot": "screenshots/successful.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1710ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1087ms", "action_id": "vjBGuN5y9x", "screenshot_filename": "vjBGuN5y9x.png", "report_screenshot": "vjBGuN5y9x.png", "resolved_screenshot": "screenshots/vjBGuN5y9x.png", "clean_action_id": "vjBGuN5y9x", "prefixed_action_id": "al_vjBGuN5y9x", "action_id_screenshot": "screenshots/vjBGuN5y9x.png"}, {"name": "Execute Test Case: apple health (8 steps)", "status": "passed", "duration": "0ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "clean_action_id": "4kBvNvFi5i", "prefixed_action_id": "al_4kBvNvFi5i", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Add Log: App is closed (with screenshot)", "status": "passed", "duration": "9ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}]}], "passed": 3, "failed": 0, "skipped": 0, "status": "passed"}