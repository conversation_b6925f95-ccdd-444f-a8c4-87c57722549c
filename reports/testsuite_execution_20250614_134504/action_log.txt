Action Log - 2025-06-14 13:49:53
================================================================================

[[13:49:53]] [INFO] Generating execution report...
[[13:49:52]] [SUCCESS] Screenshot refreshed successfully
[[13:49:52]] [SUCCESS] Screenshot refreshed
[[13:49:51]] [INFO] Refreshing screenshot...
[[13:49:49]] [SUCCESS] Screenshot refreshed successfully
[[13:49:49]] [INFO] Executing action 25/25: Add Log: App is closed (with screenshot)
[[13:49:49]] [SUCCESS] Screenshot refreshed
[[13:49:48]] [INFO] Refreshing screenshot...
[[13:49:47]] [SUCCESS] Screenshot refreshed successfully
[[13:49:47]] [SUCCESS] Screenshot refreshed
[[13:49:46]] [INFO] Refreshing screenshot...
[[13:49:44]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[13:49:43]] [SUCCESS] Screenshot refreshed successfully
[[13:49:43]] [SUCCESS] Screenshot refreshed
[[13:49:42]] [INFO] Refreshing screenshot...
[[13:49:27]] [INFO] Executing Multi Step action step 8/9: Execute Test Case: apple health (8 steps)
[[13:49:27]] [SUCCESS] Screenshot refreshed successfully
[[13:49:27]] [SUCCESS] Screenshot refreshed
[[13:49:25]] [INFO] Refreshing screenshot...
[[13:49:22]] [INFO] Executing Multi Step action step 7/9: Terminate app: com.apple.Health
[[13:49:22]] [SUCCESS] Screenshot refreshed successfully
[[13:49:22]] [SUCCESS] Screenshot refreshed
[[13:49:21]] [INFO] Refreshing screenshot...
[[13:49:18]] [INFO] Executing Multi Step action step 6/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:49:16]] [SUCCESS] Screenshot refreshed successfully
[[13:49:16]] [SUCCESS] Screenshot refreshed
[[13:49:16]] [INFO] Refreshing screenshot...
[[13:49:14]] [INFO] Executing Multi Step action step 5/9: Add Log: Clicked on Edit link successfully (with screenshot)
[[13:49:12]] [SUCCESS] Screenshot refreshed successfully
[[13:49:12]] [SUCCESS] Screenshot refreshed
[[13:49:11]] [INFO] Refreshing screenshot...
[[13:49:08]] [INFO] Executing Multi Step action step 4/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[13:49:06]] [SUCCESS] Screenshot refreshed successfully
[[13:49:06]] [SUCCESS] Screenshot refreshed
[[13:49:05]] [INFO] Refreshing screenshot...
[[13:49:02]] [INFO] Executing Multi Step action step 3/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:49:00]] [SUCCESS] Screenshot refreshed successfully
[[13:49:00]] [SUCCESS] Screenshot refreshed
[[13:49:00]] [INFO] Refreshing screenshot...
[[13:48:57]] [INFO] Executing Multi Step action step 2/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[13:48:56]] [SUCCESS] Screenshot refreshed successfully
[[13:48:56]] [SUCCESS] Screenshot refreshed
[[13:48:55]] [INFO] Refreshing screenshot...
[[13:48:50]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[13:48:50]] [INFO] Loaded 9 steps from test case: apple health
[[13:48:50]] [INFO] Loading steps for Multi Step action: apple health
[[13:48:50]] [INFO] Executing action 24/25: Execute Test Case: apple health (8 steps)
[[13:48:48]] [SUCCESS] Screenshot refreshed successfully
[[13:48:48]] [SUCCESS] Screenshot refreshed
[[13:48:47]] [INFO] Refreshing screenshot...
[[13:48:44]] [INFO] Executing action 23/25: Terminate app: com.apple.Health
[[13:48:42]] [SUCCESS] Screenshot refreshed successfully
[[13:48:42]] [SUCCESS] Screenshot refreshed
[[13:48:41]] [INFO] Refreshing screenshot...
[[13:48:38]] [INFO] Executing action 22/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:48:34]] [SUCCESS] Screenshot refreshed successfully
[[13:48:34]] [SUCCESS] Screenshot refreshed
[[13:48:34]] [INFO] Refreshing screenshot...
[[13:48:32]] [INFO] Executing action 21/25: Add Log: Clicked on Edit link successfully (with screenshot)
[[13:48:29]] [SUCCESS] Screenshot refreshed successfully
[[13:48:29]] [SUCCESS] Screenshot refreshed
[[13:48:29]] [INFO] Refreshing screenshot...
[[13:48:26]] [INFO] Executing action 20/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[13:48:24]] [SUCCESS] Screenshot refreshed successfully
[[13:48:24]] [SUCCESS] Screenshot refreshed
[[13:48:23]] [INFO] Refreshing screenshot...
[[13:48:20]] [INFO] Executing action 19/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:48:17]] [SUCCESS] Screenshot refreshed successfully
[[13:48:17]] [SUCCESS] Screenshot refreshed
[[13:48:17]] [INFO] Refreshing screenshot...
[[13:48:14]] [INFO] Executing action 18/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[13:48:12]] [SUCCESS] Screenshot refreshed successfully
[[13:48:12]] [SUCCESS] Screenshot refreshed
[[13:48:12]] [INFO] Refreshing screenshot...
[[13:47:50]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[13:47:46]] [SUCCESS] Screenshot refreshed successfully
[[13:47:46]] [SUCCESS] Screenshot refreshed
[[13:47:45]] [SUCCESS] Screenshot refreshed successfully
[[13:47:45]] [INFO] Refreshing screenshot...
[[13:47:45]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[13:47:45]] [INFO] Executing action 16/25: Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery)
[[13:47:45]] [SUCCESS] Screenshot refreshed
[[13:47:44]] [INFO] Refreshing screenshot...
[[13:47:41]] [SUCCESS] Screenshot refreshed successfully
[[13:47:41]] [SUCCESS] Screenshot refreshed
[[13:47:40]] [INFO] Refreshing screenshot...
[[13:47:38]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[13:47:37]] [SUCCESS] Screenshot refreshed successfully
[[13:47:37]] [SUCCESS] Screenshot refreshed
[[13:47:35]] [INFO] Refreshing screenshot...
[[13:47:32]] [INFO] Executing Multi Step action step 8/9: Terminate app: com.apple.Health
[[13:47:29]] [SUCCESS] Screenshot refreshed successfully
[[13:47:29]] [SUCCESS] Screenshot refreshed
[[13:47:29]] [INFO] Refreshing screenshot...
[[13:47:26]] [INFO] Executing Multi Step action step 7/9: Wait for 1 ms
[[13:47:22]] [SUCCESS] Screenshot refreshed successfully
[[13:47:22]] [SUCCESS] Screenshot refreshed
[[13:47:22]] [INFO] Refreshing screenshot...
[[13:47:20]] [INFO] Executing Multi Step action step 6/9: Add Log: Done link is clicked (with screenshot)
[[13:47:18]] [SUCCESS] Screenshot refreshed successfully
[[13:47:18]] [SUCCESS] Screenshot refreshed
[[13:47:17]] [INFO] Refreshing screenshot...
[[13:47:14]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:47:10]] [SUCCESS] Screenshot refreshed successfully
[[13:47:10]] [SUCCESS] Screenshot refreshed
[[13:47:10]] [INFO] Refreshing screenshot...
[[13:47:08]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[13:46:58]] [SUCCESS] Screenshot refreshed successfully
[[13:46:58]] [SUCCESS] Screenshot refreshed
[[13:46:58]] [INFO] Refreshing screenshot...
[[13:46:55]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[13:46:54]] [SUCCESS] Screenshot refreshed successfully
[[13:46:54]] [SUCCESS] Screenshot refreshed
[[13:46:54]] [INFO] Refreshing screenshot...
[[13:46:52]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[13:46:49]] [SUCCESS] Screenshot refreshed successfully
[[13:46:49]] [SUCCESS] Screenshot refreshed
[[13:46:49]] [INFO] Refreshing screenshot...
[[13:46:40]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[13:46:40]] [INFO] Loaded 9 steps from test case: health2
[[13:46:40]] [INFO] Loading steps for Multi Step action: health2
[[13:46:40]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[13:46:39]] [SUCCESS] Screenshot refreshed successfully
[[13:46:39]] [SUCCESS] Screenshot refreshed
[[13:46:37]] [INFO] Refreshing screenshot...
[[13:46:34]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[13:46:33]] [SUCCESS] Screenshot refreshed successfully
[[13:46:32]] [SUCCESS] Screenshot refreshed
[[13:46:31]] [INFO] Refreshing screenshot...
[[13:46:28]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[13:46:26]] [SUCCESS] Screenshot refreshed successfully
[[13:46:26]] [SUCCESS] Screenshot refreshed
[[13:46:25]] [INFO] Refreshing screenshot...
[[13:46:22]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:46:21]] [SUCCESS] Screenshot refreshed successfully
[[13:46:21]] [SUCCESS] Screenshot refreshed
[[13:46:20]] [INFO] Refreshing screenshot...
[[13:46:18]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[13:46:17]] [SUCCESS] Screenshot refreshed successfully
[[13:46:17]] [SUCCESS] Screenshot refreshed
[[13:46:17]] [INFO] Refreshing screenshot...
[[13:46:04]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[13:46:03]] [SUCCESS] Screenshot refreshed successfully
[[13:46:03]] [SUCCESS] Screenshot refreshed
[[13:46:02]] [INFO] Refreshing screenshot...
[[13:45:59]] [INFO] Executing action 9/25: Add Log: App is closed (with screenshot)
[[13:45:56]] [SUCCESS] Screenshot refreshed successfully
[[13:45:56]] [SUCCESS] Screenshot refreshed
[[13:45:54]] [INFO] Refreshing screenshot...
[[13:45:51]] [INFO] Executing action 8/25: Terminate app: com.apple.Health
[[13:45:48]] [SUCCESS] Screenshot refreshed successfully
[[13:45:48]] [SUCCESS] Screenshot refreshed
[[13:45:48]] [INFO] Refreshing screenshot...
[[13:45:45]] [INFO] Executing action 7/25: Wait for 1 ms
[[13:45:42]] [SUCCESS] Screenshot refreshed successfully
[[13:45:42]] [SUCCESS] Screenshot refreshed
[[13:45:41]] [INFO] Refreshing screenshot...
[[13:45:39]] [INFO] Executing action 6/25: Add Log: Done link is clicked (with screenshot)
[[13:45:28]] [SUCCESS] Screenshot refreshed successfully
[[13:45:28]] [SUCCESS] Screenshot refreshed
[[13:45:27]] [INFO] Refreshing screenshot...
[[13:45:24]] [INFO] Executing action 5/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:45:21]] [SUCCESS] Screenshot refreshed successfully
[[13:45:21]] [SUCCESS] Screenshot refreshed
[[13:45:20]] [INFO] Refreshing screenshot...
[[13:45:18]] [INFO] Executing action 4/25: Add Log: Edit link is clicked (with screenshot)
[[13:45:15]] [SUCCESS] Screenshot refreshed successfully
[[13:45:15]] [SUCCESS] Screenshot refreshed
[[13:45:15]] [INFO] Refreshing screenshot...
[[13:45:12]] [INFO] Executing action 3/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[13:45:11]] [SUCCESS] Screenshot refreshed successfully
[[13:45:11]] [SUCCESS] Screenshot refreshed
[[13:45:10]] [INFO] Refreshing screenshot...
[[13:45:08]] [INFO] Executing action 2/25: Add Log: Launched App Successfully (with screenshot)
[[13:45:07]] [SUCCESS] Screenshot refreshed successfully
[[13:45:07]] [SUCCESS] Screenshot refreshed
[[13:45:07]] [INFO] Refreshing screenshot...
[[13:45:04]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[13:45:04]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[13:45:04]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[13:45:04]] [INFO] Clearing screenshots from database before execution...
[[13:45:04]] [SUCCESS] All screenshots deleted successfully
[[13:45:04]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[13:45:04]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250614_134504/screenshots
[[13:45:04]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250614_134504
[[13:45:04]] [SUCCESS] Report directory initialized successfully
[[13:45:04]] [INFO] Initializing report directory and screenshots folder...
[[13:45:00]] [SUCCESS] All screenshots deleted successfully
[[13:45:00]] [INFO] All actions cleared
[[13:45:00]] [INFO] Cleaning up screenshots...
[[13:43:50]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[13:43:50]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[13:43:48]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[13:43:46]] [SUCCESS] Found 2 device(s)
[[13:43:44]] [INFO] Refreshing device list...
