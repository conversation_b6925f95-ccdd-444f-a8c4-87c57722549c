Action Log - 2025-06-14 13:47:51
================================================================================

[[13:47:50]] [INFO] Generating execution report...
[[13:47:50]] [SUCCESS] Screenshot refreshed successfully
[[13:47:50]] [SUCCESS] Screenshot refreshed
[[13:47:49]] [INFO] Refreshing screenshot...
[[13:47:47]] [SUCCESS] Screenshot refreshed successfully
[[13:47:47]] [INFO] Executing action 25/25: Add Log: App is closed (with screenshot)
[[13:47:47]] [SUCCESS] Screenshot refreshed
[[13:47:45]] [INFO] Refreshing screenshot...
[[13:47:45]] [SUCCESS] Screenshot refreshed successfully
[[13:47:45]] [SUCCESS] Screenshot refreshed
[[13:47:44]] [INFO] Refreshing screenshot...
[[13:47:42]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[13:47:41]] [SUCCESS] Screenshot refreshed successfully
[[13:47:41]] [SUCCESS] Screenshot refreshed
[[13:47:40]] [INFO] Refreshing screenshot...
[[13:47:20]] [INFO] Executing Multi Step action step 8/9: Execute Test Case: apple health (8 steps)
[[13:47:20]] [SUCCESS] Screenshot refreshed successfully
[[13:47:20]] [SUCCESS] Screenshot refreshed
[[13:47:19]] [INFO] Refreshing screenshot...
[[13:47:17]] [INFO] Executing Multi Step action step 7/9: Terminate app: com.apple.Health
[[13:47:16]] [SUCCESS] Screenshot refreshed successfully
[[13:47:16]] [SUCCESS] Screenshot refreshed
[[13:47:15]] [INFO] Refreshing screenshot...
[[13:47:11]] [INFO] Executing Multi Step action step 6/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:47:11]] [SUCCESS] Screenshot refreshed successfully
[[13:47:11]] [SUCCESS] Screenshot refreshed
[[13:47:10]] [INFO] Refreshing screenshot...
[[13:47:09]] [INFO] Executing Multi Step action step 5/9: Add Log: Clicked on Edit link successfully (with screenshot)
[[13:47:09]] [SUCCESS] Screenshot refreshed successfully
[[13:47:09]] [SUCCESS] Screenshot refreshed
[[13:47:08]] [INFO] Refreshing screenshot...
[[13:47:05]] [INFO] Executing Multi Step action step 4/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[13:47:05]] [SUCCESS] Screenshot refreshed successfully
[[13:47:05]] [SUCCESS] Screenshot refreshed
[[13:47:04]] [INFO] Refreshing screenshot...
[[13:47:01]] [INFO] Executing Multi Step action step 3/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:47:00]] [SUCCESS] Screenshot refreshed successfully
[[13:47:00]] [SUCCESS] Screenshot refreshed
[[13:47:00]] [INFO] Refreshing screenshot...
[[13:46:57]] [INFO] Executing Multi Step action step 2/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[13:46:55]] [SUCCESS] Screenshot refreshed successfully
[[13:46:55]] [SUCCESS] Screenshot refreshed
[[13:46:54]] [INFO] Refreshing screenshot...
[[13:46:49]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[13:46:49]] [INFO] Loaded 9 steps from test case: apple health
[[13:46:49]] [INFO] Loading steps for Multi Step action: apple health
[[13:46:49]] [INFO] Executing action 24/25: Execute Test Case: apple health (8 steps)
[[13:46:48]] [SUCCESS] Screenshot refreshed successfully
[[13:46:48]] [SUCCESS] Screenshot refreshed
[[13:46:48]] [INFO] Refreshing screenshot...
[[13:46:45]] [INFO] Executing action 23/25: Terminate app: com.apple.Health
[[13:46:45]] [SUCCESS] Screenshot refreshed successfully
[[13:46:45]] [SUCCESS] Screenshot refreshed
[[13:46:44]] [INFO] Refreshing screenshot...
[[13:46:40]] [INFO] Executing action 22/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:46:39]] [SUCCESS] Screenshot refreshed successfully
[[13:46:39]] [SUCCESS] Screenshot refreshed
[[13:46:38]] [INFO] Refreshing screenshot...
[[13:46:36]] [INFO] Executing action 21/25: Add Log: Clicked on Edit link successfully (with screenshot)
[[13:46:35]] [SUCCESS] Screenshot refreshed successfully
[[13:46:35]] [SUCCESS] Screenshot refreshed
[[13:46:34]] [INFO] Refreshing screenshot...
[[13:46:31]] [INFO] Executing action 20/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[13:46:30]] [SUCCESS] Screenshot refreshed successfully
[[13:46:30]] [SUCCESS] Screenshot refreshed
[[13:46:28]] [INFO] Refreshing screenshot...
[[13:46:25]] [INFO] Executing action 19/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:46:24]] [SUCCESS] Screenshot refreshed successfully
[[13:46:24]] [SUCCESS] Screenshot refreshed
[[13:46:24]] [INFO] Refreshing screenshot...
[[13:46:21]] [INFO] Executing action 18/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[13:46:20]] [SUCCESS] Screenshot refreshed successfully
[[13:46:20]] [SUCCESS] Screenshot refreshed
[[13:46:20]] [INFO] Refreshing screenshot...
[[13:46:07]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[13:46:06]] [SUCCESS] Screenshot refreshed successfully
[[13:46:06]] [SUCCESS] Screenshot refreshed
[[13:46:05]] [SUCCESS] Screenshot refreshed successfully
[[13:46:05]] [INFO] Refreshing screenshot...
[[13:46:05]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[13:46:05]] [INFO] Executing action 16/25: Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery)
[[13:46:05]] [SUCCESS] Screenshot refreshed
[[13:46:04]] [INFO] Refreshing screenshot...
[[13:45:59]] [SUCCESS] Screenshot refreshed successfully
[[13:45:59]] [SUCCESS] Screenshot refreshed
[[13:45:57]] [INFO] Refreshing screenshot...
[[13:45:55]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[13:45:55]] [SUCCESS] Screenshot refreshed successfully
[[13:45:55]] [SUCCESS] Screenshot refreshed
[[13:45:53]] [INFO] Refreshing screenshot...
[[13:45:50]] [INFO] Executing Multi Step action step 8/9: Terminate app: com.apple.Health
[[13:45:49]] [SUCCESS] Screenshot refreshed successfully
[[13:45:49]] [SUCCESS] Screenshot refreshed
[[13:45:49]] [INFO] Refreshing screenshot...
[[13:45:46]] [INFO] Executing Multi Step action step 7/9: Wait for 1 ms
[[13:45:46]] [SUCCESS] Screenshot refreshed successfully
[[13:45:46]] [SUCCESS] Screenshot refreshed
[[13:45:45]] [INFO] Refreshing screenshot...
[[13:45:44]] [INFO] Executing Multi Step action step 6/9: Add Log: Done link is clicked (with screenshot)
[[13:45:43]] [SUCCESS] Screenshot refreshed successfully
[[13:45:43]] [SUCCESS] Screenshot refreshed
[[13:45:42]] [INFO] Refreshing screenshot...
[[13:45:39]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:45:39]] [SUCCESS] Screenshot refreshed successfully
[[13:45:39]] [SUCCESS] Screenshot refreshed
[[13:45:38]] [INFO] Refreshing screenshot...
[[13:45:37]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[13:45:36]] [SUCCESS] Screenshot refreshed successfully
[[13:45:36]] [SUCCESS] Screenshot refreshed
[[13:45:35]] [INFO] Refreshing screenshot...
[[13:45:32]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[13:45:32]] [SUCCESS] Screenshot refreshed successfully
[[13:45:32]] [SUCCESS] Screenshot refreshed
[[13:45:31]] [INFO] Refreshing screenshot...
[[13:45:30]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[13:45:29]] [SUCCESS] Screenshot refreshed successfully
[[13:45:29]] [SUCCESS] Screenshot refreshed
[[13:45:29]] [INFO] Refreshing screenshot...
[[13:45:24]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[13:45:24]] [INFO] Loaded 9 steps from test case: health2
[[13:45:24]] [INFO] Loading steps for Multi Step action: health2
[[13:45:24]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[13:45:23]] [SUCCESS] Screenshot refreshed successfully
[[13:45:23]] [SUCCESS] Screenshot refreshed
[[13:45:23]] [INFO] Refreshing screenshot...
[[13:45:21]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[13:45:21]] [SUCCESS] Screenshot refreshed successfully
[[13:45:21]] [SUCCESS] Screenshot refreshed
[[13:45:20]] [INFO] Refreshing screenshot...
[[13:45:18]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[13:45:17]] [SUCCESS] Screenshot refreshed successfully
[[13:45:17]] [SUCCESS] Screenshot refreshed
[[13:45:17]] [INFO] Refreshing screenshot...
[[13:45:14]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:45:13]] [SUCCESS] Screenshot refreshed successfully
[[13:45:13]] [SUCCESS] Screenshot refreshed
[[13:45:12]] [INFO] Refreshing screenshot...
[[13:45:10]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[13:45:06]] [SUCCESS] Screenshot refreshed successfully
[[13:45:06]] [SUCCESS] Screenshot refreshed
[[13:45:05]] [INFO] Refreshing screenshot...
[[13:44:51]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[13:44:50]] [SUCCESS] Screenshot refreshed successfully
[[13:44:50]] [SUCCESS] Screenshot refreshed
[[13:44:49]] [INFO] Refreshing screenshot...
[[13:44:47]] [INFO] Executing action 9/25: Add Log: App is closed (with screenshot)
[[13:44:46]] [SUCCESS] Screenshot refreshed successfully
[[13:44:46]] [SUCCESS] Screenshot refreshed
[[13:44:45]] [INFO] Refreshing screenshot...
[[13:44:42]] [INFO] Executing action 8/25: Terminate app: com.apple.Health
[[13:44:42]] [SUCCESS] Screenshot refreshed successfully
[[13:44:42]] [SUCCESS] Screenshot refreshed
[[13:44:40]] [INFO] Refreshing screenshot...
[[13:44:37]] [INFO] Executing action 7/25: Wait for 1 ms
[[13:44:37]] [SUCCESS] Screenshot refreshed successfully
[[13:44:37]] [SUCCESS] Screenshot refreshed
[[13:44:36]] [INFO] Refreshing screenshot...
[[13:44:33]] [INFO] Executing action 6/25: Add Log: Done link is clicked (with screenshot)
[[13:44:33]] [SUCCESS] Screenshot refreshed successfully
[[13:44:33]] [SUCCESS] Screenshot refreshed
[[13:44:32]] [INFO] Refreshing screenshot...
[[13:44:28]] [INFO] Executing action 5/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:44:27]] [SUCCESS] Screenshot refreshed successfully
[[13:44:27]] [SUCCESS] Screenshot refreshed
[[13:44:26]] [INFO] Refreshing screenshot...
[[13:44:24]] [INFO] Executing action 4/25: Add Log: Edit link is clicked (with screenshot)
[[13:44:23]] [SUCCESS] Screenshot refreshed successfully
[[13:44:23]] [SUCCESS] Screenshot refreshed
[[13:44:22]] [INFO] Refreshing screenshot...
[[13:44:18]] [INFO] Executing action 3/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[13:44:18]] [SUCCESS] Screenshot refreshed successfully
[[13:44:18]] [SUCCESS] Screenshot refreshed
[[13:44:17]] [INFO] Refreshing screenshot...
[[13:44:14]] [INFO] Executing action 2/25: Add Log: Launched App Successfully (with screenshot)
[[13:44:14]] [SUCCESS] Screenshot refreshed successfully
[[13:44:14]] [SUCCESS] Screenshot refreshed
[[13:44:12]] [INFO] Refreshing screenshot...
[[13:44:08]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[13:44:08]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[13:44:08]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[13:44:08]] [INFO] Clearing screenshots from database before execution...
[[13:44:08]] [SUCCESS] All screenshots deleted successfully
[[13:44:08]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[13:44:08]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250614_134408/screenshots
[[13:44:08]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250614_134408
[[13:44:08]] [SUCCESS] Report directory initialized successfully
[[13:44:08]] [INFO] Initializing report directory and screenshots folder...
[[13:43:56]] [SUCCESS] All screenshots deleted successfully
[[13:43:56]] [INFO] All actions cleared
[[13:43:56]] [INFO] Cleaning up screenshots...
[[13:43:40]] [SUCCESS] Connected to device: 00008030-00020C123E60402E with AirTest support
[[13:43:40]] [INFO] Device info updated: 00008030-00020C123E60402E
[[13:43:37]] [INFO] Connecting to device: 00008030-00020C123E60402E (Platform: iOS)...
[[13:43:33]] [SUCCESS] Found 2 device(s)
[[13:43:32]] [INFO] Refreshing device list...
