Action Log - 2025-06-14 13:35:03
================================================================================

[[13:35:02]] [INFO] Generating execution report...
[[13:35:02]] [INFO] Skipping remaining steps in failed test case (moving from action 17 to next test case at 25)
[[13:35:02]] [ERROR] Action 17 failed: No device connected
[[13:34:52]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[13:34:52]] [INFO] Skipping remaining steps in failed test case (moving from action 15 to next test case at 16)
[[13:34:52]] [INFO] Moving to the next test case after failure (server will handle retry)
[[13:34:52]] [ERROR] Multi Step action step 1 failed: No device connected
[[13:34:50]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[13:34:50]] [INFO] Loaded 9 steps from test case: health2
[[13:34:50]] [INFO] Loading steps for Multi Step action: health2
[[13:34:50]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[13:34:50]] [ERROR] All Hook Actions failed: No device connected
[[13:34:48]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[13:34:48]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[13:34:48]] [ERROR] Action 14 failed: No device connected
[[13:34:48]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[13:34:48]] [ERROR] All Hook Actions failed: No device connected
[[13:34:46]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[13:34:46]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[13:34:46]] [ERROR] Action 13 failed: No device connected
[[13:34:46]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[13:34:46]] [ERROR] All Hook Actions failed: No device connected
[[13:34:44]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[13:34:44]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[13:34:44]] [ERROR] Action 12 failed: No device connected
[[13:34:44]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[13:34:44]] [ERROR] All Hook Actions failed: No device connected
[[13:34:42]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[13:34:42]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[13:34:42]] [ERROR] Action 11 failed: No device connected
[[13:34:42]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[13:34:42]] [ERROR] All Hook Actions failed: No device connected
[[13:34:40]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[13:34:40]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[13:34:40]] [ERROR] Action 10 failed: No device connected
[[13:34:30]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[13:34:30]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to next test case at 9)
[[13:34:30]] [ERROR] Action 1 failed: No device connected
[[13:34:30]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[13:34:30]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[13:34:30]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[13:34:30]] [INFO] Clearing screenshots from database before execution...
[[13:34:30]] [SUCCESS] All screenshots deleted successfully
[[13:34:30]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[13:34:30]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250614_133430/screenshots
[[13:34:30]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250614_133430
[[13:34:30]] [SUCCESS] Report directory initialized successfully
[[13:34:30]] [INFO] Initializing report directory and screenshots folder...
[[13:34:01]] [SUCCESS] All screenshots deleted successfully
[[13:34:01]] [INFO] All actions cleared
[[13:34:01]] [INFO] Cleaning up screenshots...
[[13:32:33]] [SUCCESS] Connected to device: 00008030-00020C123E60402E with AirTest support
[[13:32:33]] [INFO] Device info updated: 00008030-00020C123E60402E
[[13:32:30]] [INFO] Connecting to device: 00008030-00020C123E60402E (Platform: iOS)...
[[13:32:29]] [SUCCESS] Found 2 device(s)
[[13:32:27]] [INFO] Refreshing device list...
