#!/usr/bin/env python3
"""
Test script to verify session isolation in multi-device setup
"""

import requests
import time
import json

def test_session_isolation():
    """Test that different Flask sessions maintain separate device connections"""
    
    # Test with two different sessions
    session1 = requests.Session()
    session2 = requests.Session()
    
    base_url_1 = "http://localhost:8080"
    base_url_2 = "http://localhost:8081"
    
    print("Testing session isolation between two app instances...")
    print(f"Session 1: {base_url_1}")
    print(f"Session 2: {base_url_2}")
    
    # Test 1: Check that sessions can get different device lists
    print("\n1. Testing device list retrieval...")
    
    try:
        devices1 = session1.get(f"{base_url_1}/api/devices").json()
        devices2 = session2.get(f"{base_url_2}/api/devices").json()
        
        print(f"Session 1 devices: {len(devices1.get('devices', []))} found")
        print(f"Session 2 devices: {len(devices2.get('devices', []))} found")
        
        if devices1.get('devices') and devices2.get('devices'):
            device1_id = devices1['devices'][0]['id']
            device2_id = devices2['devices'][0]['id'] if len(devices2['devices']) > 1 else devices1['devices'][0]['id']
            
            print(f"Will test with Device 1: {device1_id}")
            print(f"Will test with Device 2: {device2_id}")
            
            # Test 2: Connect to different devices in each session
            print("\n2. Testing device connections...")
            
            # Connect session 1 to device 1
            connect1_data = {
                "device_id": device1_id,
                "platform": devices1['devices'][0].get('platform', 'iOS'),
                "session_id": "test_session_1"
            }
            
            response1 = session1.post(f"{base_url_1}/api/device/connect", json=connect1_data)
            print(f"Session 1 connect response: {response1.status_code}")
            if response1.status_code == 200:
                result1 = response1.json()
                print(f"Session 1 connected to: {result1.get('device_id', 'unknown')}")
            
            # Wait a moment
            time.sleep(2)
            
            # Connect session 2 to device 2 (or same device to test isolation)
            connect2_data = {
                "device_id": device2_id,
                "platform": devices2['devices'][0].get('platform', 'iOS'),
                "session_id": "test_session_2"
            }
            
            response2 = session2.post(f"{base_url_2}/api/device/connect", json=connect2_data)
            print(f"Session 2 connect response: {response2.status_code}")
            if response2.status_code == 200:
                result2 = response2.json()
                print(f"Session 2 connected to: {result2.get('device_id', 'unknown')}")
            
            # Test 3: Verify session isolation by checking screenshots
            print("\n3. Testing screenshot isolation...")
            
            # Get screenshot from session 1
            screenshot1 = session1.get(f"{base_url_1}/screenshot?deviceId={device1_id}&sessionId=test_session_1")
            print(f"Session 1 screenshot response: {screenshot1.status_code}")
            
            # Get screenshot from session 2
            screenshot2 = session2.get(f"{base_url_2}/screenshot?deviceId={device2_id}&sessionId=test_session_2")
            print(f"Session 2 screenshot response: {screenshot2.status_code}")
            
            # Test 4: Verify that sessions maintain their own device state
            print("\n4. Testing session state isolation...")
            
            # Check session info for both sessions
            session_info1 = session1.get(f"{base_url_1}/api/session_info")
            session_info2 = session2.get(f"{base_url_2}/api/session_info")
            
            print(f"Session 1 info response: {session_info1.status_code}")
            print(f"Session 2 info response: {session_info2.status_code}")
            
            if session_info1.status_code == 200 and session_info2.status_code == 200:
                info1 = session_info1.json()
                info2 = session_info2.json()
                
                session1_device = info1.get('capabilities', {}).get('udid', 'unknown')
                session2_device = info2.get('capabilities', {}).get('udid', 'unknown')
                
                print(f"Session 1 is connected to device: {session1_device}")
                print(f"Session 2 is connected to device: {session2_device}")
                
                if device1_id != device2_id:
                    if session1_device != session2_device:
                        print("✅ SUCCESS: Sessions are properly isolated with different devices!")
                    else:
                        print("❌ FAILURE: Sessions are showing the same device despite connecting to different ones!")
                else:
                    print("ℹ️  NOTE: Both sessions connected to the same device - isolation test inconclusive")
            
            print("\n5. Cleaning up...")
            
            # Disconnect both sessions
            session1.post(f"{base_url_1}/api/device/disconnect")
            session2.post(f"{base_url_2}/api/device/disconnect")
            
            print("Test completed!")
            
        else:
            print("❌ No devices found for testing")
            
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error: {e}")
        print("Make sure both app instances are running:")
        print("  Terminal 1: python run.py")
        print("  Terminal 2: python run.py --port 8081 --appium-port 4724")
    except Exception as e:
        print(f"❌ Error during testing: {e}")

if __name__ == "__main__":
    test_session_isolation()
