/**
 * Export Run Manager
 * 
 * Handles the export functionality to create a custom HTML report
 */

class ExportRunManager {
    constructor() {
        // Initialize properties
        this.exportButton = null;
        this.latestReportId = null;
        this.isExecuting = false;
        
        // Bind methods
        this.init = this.init.bind(this);
        this.createExportButton = this.createExportButton.bind(this);
        this.handleExportClick = this.handleExportClick.bind(this);
        this.updateExportButtonState = this.updateExportButtonState.bind(this);
        
        // Initialize
        this.init();
    }
    
    init() {
        // Create the export button and add it to the UI
        this.createExportButton();
        
        // Listen for execution events to update button state
        document.addEventListener('execution-started', () => {
            console.log('Export Manager: Execution started event received');
            this.isExecuting = true;
            this.updateExportButtonState();
        });

        document.addEventListener('execution-completed', () => {
            console.log('Export Manager: Execution completed event received');
            this.isExecuting = false;
            console.log('Export Manager: Setting isExecuting to false and checking for latest report');
            this.updateExportButtonState(); // Update button state immediately

            // Add multiple checks to ensure we catch the report
            setTimeout(() => {
                console.log('Export Manager: First check (1s) - checking for latest report...');
                this.checkForLatestReport();
            }, 1000);

            setTimeout(() => {
                console.log('Export Manager: Second check (3s) - checking for latest report...');
                this.checkForLatestReport();
            }, 3000);

            setTimeout(() => {
                console.log('Export Manager: Third check (5s) - checking for latest report...');
                this.checkForLatestReport();
            }, 5000);
        });
        
        // Check if there's a latest report available on load
        this.checkForLatestReport();
        
        // Also manually check for reports every 5 seconds as a fallback
        setInterval(() => {
            if (!this.isExecuting) {
                this.checkForLatestReport();
            }
        }, 5000);
    }
    
    createExportButton() {
        // Create export button and add it to the UI
        const buttonContainer = document.querySelector('.d-flex.justify-content-end.mb-3');

        console.log('Export Manager: Looking for button container...', buttonContainer);

        if (buttonContainer) {
            // Check if button already exists
            const existingButton = document.getElementById('exportRunBtn');
            if (existingButton) {
                console.log('Export Manager: Button already exists, using existing button');
                this.exportButton = existingButton;
                this.exportButton.addEventListener('click', this.handleExportClick);
                return;
            }

            this.exportButton = document.createElement('button');
            this.exportButton.id = 'exportRunBtn';
            this.exportButton.className = 'btn btn-info me-2';
            this.exportButton.innerHTML = '<i class="bi bi-file-earmark-zip"></i> Export Run';
            this.exportButton.disabled = true;
            this.exportButton.addEventListener('click', this.handleExportClick);

            // Insert the button before the Clear All button
            const clearButton = document.getElementById('clearActions');
            if (clearButton) {
                buttonContainer.insertBefore(this.exportButton, clearButton);
                console.log('Export Manager: Button inserted before Clear All button');
            } else {
                buttonContainer.appendChild(this.exportButton);
                console.log('Export Manager: Button appended to container');
            }
            console.log('Export Manager: Button created and added to UI');
        } else {
            console.error('Button container not found for Export Run button');
            console.log('Available containers:', document.querySelectorAll('.d-flex'));
        }
    }
    
    async checkForLatestReport() {
        try {
            console.log('Export Manager: Checking for latest report...');
            const response = await fetch('/api/reports/latest');
            const data = await response.json();

            console.log('Export Manager: Latest report response:', data);
            console.log('Export Manager: Response status:', response.status);

            // Reset report ID first
            this.latestReportId = null;

            // Handle different possible response formats
            if (data.success && data.report && data.report.report_id) {
                this.latestReportId = data.report.report_id;
                console.log('Export Manager: Found report ID from data.report.report_id:', this.latestReportId);
                this.updateExportButtonState();
            } else if (data.status === 'success' && data.report_url) {
                // Extract report ID from URL if available
                const urlParts = data.report_url.split('/');
                console.log('Export Manager: URL parts:', urlParts);
                if (urlParts.length > 0) {
                    const lastPart = urlParts[urlParts.length - 2]; // Get second to last part of URL
                    console.log('Export Manager: Checking URL part:', lastPart);
                    if (lastPart && lastPart.startsWith('testsuite_execution_')) {
                        this.latestReportId = lastPart;
                        console.log('Export Manager: Extracted report ID from URL:', this.latestReportId);
                        this.updateExportButtonState();
                    } else {
                        console.log('Export Manager: URL part does not start with testsuite_execution_');
                    }
                } else {
                    console.log('Export Manager: No URL parts found');
                }
            } else if (data.status === 'success' && data.message === 'No reports found') {
                console.log('Export Manager: No reports found - button will remain disabled');
                this.updateExportButtonState();
            } else {
                console.log('Export Manager: Unexpected response format:', data);
                this.updateExportButtonState();
            }
        } catch (error) {
            console.error('Export Manager: Error checking for latest report:', error);
            this.latestReportId = null;
            this.updateExportButtonState();
        }
    }
    
    updateExportButtonState() {
        console.log('Export Manager: updateExportButtonState called');
        console.log('Export Manager: exportButton exists:', !!this.exportButton);
        console.log('Export Manager: isExecuting:', this.isExecuting);
        console.log('Export Manager: latestReportId:', this.latestReportId);

        if (!this.exportButton) {
            console.error('Export Manager: No export button found!');
            return;
        }

        // Enable the button only if we have a report and not currently executing
        const shouldEnable = !this.isExecuting && this.latestReportId;
        this.exportButton.disabled = !shouldEnable;

        console.log('Export Manager: Button state updated - enabled:', shouldEnable,
                  'isExecuting:', this.isExecuting, 'latestReportId:', this.latestReportId);
        console.log('Export Manager: Button disabled attribute:', this.exportButton.disabled);
    }
    
    async handleExportClick() {
        if (!this.latestReportId) {
            console.error('No report ID available for export');
            return;
        }
        
        // Store reference to this for use in async context
        const self = this;
        
        try {
            // Show loading state
            this.exportButton.disabled = true;
            this.exportButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Exporting...';
            
            // Call the API to generate the custom export
            const response = await fetch(`/api/reports/export/${this.latestReportId}`, {
                method: 'POST'
            });
            
            const data = await response.json();
            console.log('Export response:', data);
            
            if (data.success && data.download_url) {
                // Add timestamp to filename to prevent browser caching
                const timestamp = new Date().getTime();
                const downloadUrl = `${data.download_url}?t=${timestamp}`;
                
                // Create a temporary link and trigger download
                const downloadLink = document.createElement('a');
                downloadLink.href = downloadUrl;
                downloadLink.download = data.filename || 'test-execution-report.zip';
                downloadLink.target = '_blank'; // Open in new tab as fallback
                document.body.appendChild(downloadLink);
                
                // Attempt download with retry
                let downloadSuccess = false;
                let retryCount = 0;
                const maxRetries = 3;
                
                while (!downloadSuccess && retryCount < maxRetries) {
                    try {
                        console.log(`Download attempt ${retryCount + 1}...`);
                        downloadLink.click();
                        
                        // Add a delay to see if the download starts
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        
                        // We can't programmatically detect if the download succeeded,
                        // so we'll assume it did and show a message
                        downloadSuccess = true;
                    } catch (downloadError) {
                        console.error(`Download attempt ${retryCount + 1} failed:`, downloadError);
                        retryCount++;
                        
                        // Wait a bit longer before retry
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
                
                document.body.removeChild(downloadLink);
                
                if (downloadSuccess) {
                    // Show success toast
                    showToast('Export Started', 'The export file should download shortly. If it doesn\'t, click the link in the toast.', 'success', () => {
                        window.open(downloadUrl, '_blank');
                    });
                } else {
                    // Show fallback message with direct link
                    showToast('Export Ready', 'Click here to download the export file manually.', 'info', () => {
                        window.open(downloadUrl, '_blank');
                    });
                }
            } else {
                throw new Error(data.message || 'Failed to generate export');
            }
        } catch (error) {
            console.error('Error exporting run:', error);
            showToast('Export Failed', error.message, 'error');
        } finally {
            // Reset button state
            self.exportButton.disabled = false;
            self.exportButton.innerHTML = '<i class="bi bi-file-earmark-zip"></i> Export Run';
        }
    }
}

// Helper function to show a toast notification
function showToast(title, message, type = 'info', callback) {
    if (window.app && typeof window.app.showToast === 'function') {
        window.app.showToast(title, message, type, callback);
    } else {
        // Fallback if app.showToast is not available
        const toastContainer = document.getElementById('toastContainer') || createToastContainer();
        
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        // Add pointer cursor if there's a callback
        if (callback) {
            toast.style.cursor = 'pointer';
            toast.addEventListener('click', callback);
        }
        
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${title}</strong><br>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toastContainer';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    container.style.zIndex = '1050';
    document.body.appendChild(container);
    return container;
}

// Initialize the export manager
document.addEventListener('DOMContentLoaded', () => {
    console.log('Export Manager: Initializing');
    window.exportRunManager = new ExportRunManager();
}); 