#!/usr/bin/env python3
"""
Quick test to verify device isolation fix is working
"""

import requests
import json

def test_device_isolation_fix():
    """Test that device isolation is working with the new fix"""
    
    # Test with a single session first
    session = requests.Session()
    base_url = "http://localhost:8080"
    
    print("Testing device isolation fix...")
    print(f"Testing with: {base_url}")
    
    try:
        # 1. Get devices
        print("\n1. Getting device list...")
        devices_response = session.get(f"{base_url}/api/devices")
        devices_data = devices_response.json()
        
        if not devices_data.get('devices'):
            print("❌ No devices found")
            return
            
        device = devices_data['devices'][0]
        device_id = device['id']
        platform = device.get('platform', 'iOS')
        
        print(f"✅ Found device: {device_id} ({platform})")
        
        # 2. Connect to device
        print("\n2. Connecting to device...")
        connect_response = session.post(f"{base_url}/api/device/connect", json={
            "device_id": device_id,
            "platform": platform,
            "session_id": "test_session"
        })
        
        if connect_response.status_code == 200:
            connect_data = connect_response.json()
            print(f"✅ Connected: {connect_data.get('status')}")
        else:
            print(f"❌ Connection failed: {connect_response.status_code}")
            return
        
        # 3. Test action execution with device_id
        print("\n3. Testing action execution...")
        test_action = {
            "type": "addLog",
            "message": "Test isolation fix",
            "action_id": "test123"
        }
        
        action_response = session.post(f"{base_url}/api/action/execute", json={
            "action": test_action,
            "device_id": device_id,
            "force_screenshot": True
        })
        
        if action_response.status_code == 200:
            action_data = action_response.json()
            if action_data.get('success'):
                print("✅ Action executed successfully with device_id")
            else:
                print(f"❌ Action failed: {action_data.get('error')}")
        else:
            print(f"❌ Action request failed: {action_response.status_code}")
            try:
                error_data = action_response.json()
                print(f"   Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   Raw response: {action_response.text}")
        
        # 4. Test action execution without device_id (should use session)
        print("\n4. Testing action execution without device_id...")
        action_response2 = session.post(f"{base_url}/api/action/execute", json={
            "action": test_action,
            "force_screenshot": True
        })
        
        if action_response2.status_code == 200:
            action_data2 = action_response2.json()
            if action_data2.get('success'):
                print("✅ Action executed successfully using session fallback")
            else:
                print(f"❌ Action failed: {action_data2.get('error')}")
        else:
            print(f"❌ Action request failed: {action_response2.status_code}")
            try:
                error_data2 = action_response2.json()
                print(f"   Error: {error_data2.get('error', 'Unknown error')}")
            except:
                print(f"   Raw response: {action_response2.text}")
        
        # 5. Test screenshot endpoint
        print("\n5. Testing screenshot endpoint...")
        screenshot_response = session.get(f"{base_url}/screenshot?deviceId={device_id}")
        
        if screenshot_response.status_code == 200:
            print("✅ Screenshot retrieved successfully")
        else:
            print(f"❌ Screenshot failed: {screenshot_response.status_code}")
        
        # 6. Disconnect
        print("\n6. Disconnecting...")
        disconnect_response = session.post(f"{base_url}/api/device/disconnect", json={
            "deviceId": device_id
        })
        
        if disconnect_response.status_code == 200:
            print("✅ Disconnected successfully")
        else:
            print(f"❌ Disconnect failed: {disconnect_response.status_code}")
        
        print("\n✅ Device isolation fix test completed!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection error: Make sure the app is running on port 8080")
        print("   Start with: python run.py")
    except Exception as e:
        print(f"❌ Error during testing: {e}")

if __name__ == "__main__":
    test_device_isolation_fix()
